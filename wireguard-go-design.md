# WireGuard Go 管理系统架构设计

## 项目概述

基于 Go + Gin + MongoDB + Redis 的 WireGuard 管理系统，实现用户间完全网络隔离。每个用户拥有独立的 WireGuard 服务器实例，管理员可统一管理所有用户服务器。

## 核心设计原则

### 1. 用户隔离策略
- **一用户一服务器**: 每个用户只能创建一个 WireGuard 服务器实例
- **命名空间隔离**: 每个用户服务器运行在独立的网络命名空间中
- **资源隔离**: IP 段、端口、配置文件完全隔离
- **权限隔离**: 用户只能管理自己的服务器和客户端

### 2. 管理员权限设计
- **全局管理**: 管理员可查看和管理所有用户的服务器
- **系统监控**: 监控所有服务器状态、流量统计、连接数
- **用户管理**: 启用/禁用用户服务器，设置资源限制
- **系统配置**: 全局网络配置、安全策略、资源分配

### 3. 网络架构设计
- **基础网段**: ********/16 作为总体网段
- **用户分配**: 每个用户分配 /24 子网 (10.8.{userIndex}.0/24)
- **端口分配**: 基础端口 51820 + 用户索引
- **DNS 配置**: 支持用户自定义和系统默认 DNS

## 系统架构设计

### 1. 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    API Gateway Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   User APIs     │  │  Admin APIs     │  │  Public APIs    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Business Logic Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Server Manager  │  │ Client Manager  │  │ Stats Collector │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │Config Generator │  │ Network Manager │  │  Cache Manager  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────┐
│                   Infrastructure Layer                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │    MongoDB      │  │     Redis       │  │ Network Namespaces│ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2. 数据模型设计

#### 核心实体关系
```
User (1) ──── (1) UserWGServer ──── (1:N) WGClient
  │                    │
  │                    └──── (1:N) ServerStats
  │
  └──── (1:N) UserActivity ──── (1:N) AuditLog
```

#### 数据库集合设计

**用户服务器集合 (user_wg_servers)**
- 每个用户只能有一条记录
- 包含服务器基础配置、网络配置、状态信息
- 支持管理员全局管理和用户自管理

**客户端集合 (wg_clients)**
- 属于特定用户的服务器
- 包含客户端配置、连接状态、流量统计
- 支持批量操作和状态管理

**系统配置集合 (system_configs)**
- 全局系统配置
- 网络分配策略
- 资源限制策略

**审计日志集合 (audit_logs)**
- 记录所有关键操作
- 支持管理员审计和用户操作历史

### 3. 权限模型设计

#### 角色定义
- **超级管理员**: 系统全部权限
- **普通管理员**: 用户管理、服务器管理权限
- **普通用户**: 仅自己服务器和客户端管理权限

#### 权限矩阵
```
操作类型          │ 超级管理员 │ 普通管理员 │ 普通用户
─────────────────┼──────────┼──────────┼────────
创建用户服务器     │    ✓     │    ✓     │   ✓*
删除用户服务器     │    ✓     │    ✓     │   ✓*
查看所有服务器     │    ✓     │    ✓     │   ✗
管理他人客户端     │    ✓     │    ✓     │   ✗
系统配置管理      │    ✓     │    ✗     │   ✗
审计日志查看      │    ✓     │    ✓     │   ✗
```
*仅限自己的资源

## 网络隔离设计

### 1. 命名空间隔离架构

```
Host Network Namespace
├── ns-user-001 (用户1网络空间)
│   ├── wg-user-001 (WireGuard接口)
│   ├── IP: ********/24
│   └── Port: 51821
├── ns-user-002 (用户2网络空间)
│   ├── wg-user-002 (WireGuard接口)
│   ├── IP: ********/24
│   └── Port: 51822
└── ns-admin (管理网络空间)
    ├── 监控所有用户网络
    └── 统计收集接口
```

### 2. IP 地址分配策略

#### 网络规划
- **总体网段**: ********/16 (支持 65534 个用户)
- **用户网段**: 10.8.{userIndex}.0/24 (每用户 254 个客户端)
- **服务器地址**: 10.8.{userIndex}.1 (网关地址)
- **客户端地址**: 10.8.{userIndex}.2-254

#### 端口分配策略
- **基础端口**: 51820 (系统保留)
- **用户端口**: 51821 + userIndex
- **管理端口**: 51820 (监控和管理)

### 3. 防火墙隔离规则

#### 用户间隔离
- 禁止跨命名空间通信
- 每个用户只能访问自己的网段
- 管理员可访问所有网段进行监控

#### 流量控制
- 入站流量限制
- 出站流量限制
- 连接数限制
- 带宽限制

## 功能模块设计

### 1. 用户功能模块

#### 服务器管理
- **创建服务器**: 自动分配网络资源，创建命名空间
- **配置服务器**: 修改基础配置（名称、DNS、MTU等）
- **启用/禁用**: 控制服务器运行状态
- **删除服务器**: 清理所有相关资源

#### 客户端管理
- **添加客户端**: 自动分配IP，生成密钥对
- **配置客户端**: 修改允许IP、保持连接等参数
- **启用/禁用**: 控制客户端连接权限
- **批量操作**: 支持批量启用/禁用/删除

#### 配置下载
- **配置文件**: 生成标准 WireGuard 配置文件
- **QR码生成**: 移动端快速导入
- **批量导出**: 导出所有客户端配置

### 2. 管理员功能模块

#### 全局服务器管理
- **服务器列表**: 查看所有用户的服务器状态
- **批量操作**: 批量启用/禁用用户服务器
- **资源监控**: 监控服务器资源使用情况
- **强制操作**: 强制重启/停止用户服务器

#### 用户管理
- **用户列表**: 查看所有用户及其服务器状态
- **权限控制**: 设置用户资源限制
- **服务器代管**: 代替用户管理服务器配置
- **批量分配**: 批量为用户创建服务器

#### 系统监控
- **实时统计**: 全局流量、连接数、在线用户
- **性能监控**: 系统资源使用情况
- **告警管理**: 异常情况告警通知
- **报表生成**: 定期生成使用报表

## API 接口设计

### 1. 用户 API 接口

#### 服务器管理接口
```
GET    /api/v1/user/server              # 获取用户服务器信息
POST   /api/v1/user/server              # 创建用户服务器
PUT    /api/v1/user/server              # 更新服务器配置
DELETE /api/v1/user/server              # 删除服务器
POST   /api/v1/user/server/enable       # 启用服务器
POST   /api/v1/user/server/disable      # 禁用服务器
POST   /api/v1/user/server/restart      # 重启服务器
```

#### 客户端管理接口
```
GET    /api/v1/user/clients             # 获取客户端列表
POST   /api/v1/user/clients             # 创建客户端
GET    /api/v1/user/clients/:id         # 获取客户端详情
PUT    /api/v1/user/clients/:id         # 更新客户端
DELETE /api/v1/user/clients/:id         # 删除客户端
POST   /api/v1/user/clients/:id/enable  # 启用客户端
POST   /api/v1/user/clients/:id/disable # 禁用客户端
POST   /api/v1/user/clients/batch       # 批量操作客户端
```

#### 配置下载接口
```
GET    /api/v1/user/clients/:id/config    # 下载配置文件
GET    /api/v1/user/clients/:id/qrcode    # 获取QR码
GET    /api/v1/user/configs/export        # 批量导出配置
```

#### 统计信息接口
```
GET    /api/v1/user/stats/server         # 服务器统计
GET    /api/v1/user/stats/clients        # 客户端统计
GET    /api/v1/user/stats/traffic        # 流量统计
```

### 2. 管理员 API 接口

#### 全局服务器管理
```
GET    /api/v1/admin/servers             # 获取所有服务器列表
GET    /api/v1/admin/servers/:userId     # 获取指定用户服务器
POST   /api/v1/admin/servers/:userId     # 为用户创建服务器
PUT    /api/v1/admin/servers/:userId     # 更新用户服务器
DELETE /api/v1/admin/servers/:userId     # 删除用户服务器
POST   /api/v1/admin/servers/batch       # 批量操作服务器
```

#### 用户管理
```
GET    /api/v1/admin/users               # 获取用户列表
GET    /api/v1/admin/users/:id/server    # 获取用户服务器详情
POST   /api/v1/admin/users/:id/limits    # 设置用户限制
GET    /api/v1/admin/users/:id/stats     # 获取用户统计
POST   /api/v1/admin/users/batch-create  # 批量创建用户服务器
```

#### 客户端管理
```
GET    /api/v1/admin/clients             # 获取所有客户端
GET    /api/v1/admin/clients/:userId     # 获取用户客户端列表
POST   /api/v1/admin/clients/:userId     # 为用户创建客户端
PUT    /api/v1/admin/clients/:id         # 更新客户端
DELETE /api/v1/admin/clients/:id         # 删除客户端
POST   /api/v1/admin/clients/batch       # 批量操作客户端
```

#### 系统监控
```
GET    /api/v1/admin/stats/overview      # 系统概览统计
GET    /api/v1/admin/stats/servers       # 服务器统计
GET    /api/v1/admin/stats/traffic       # 流量统计
GET    /api/v1/admin/stats/resources     # 资源使用统计
GET    /api/v1/admin/logs/audit          # 审计日志
GET    /api/v1/admin/alerts              # 系统告警
```

#### 系统配置
```
GET    /api/v1/admin/config/system       # 获取系统配置
PUT    /api/v1/admin/config/system       # 更新系统配置
GET    /api/v1/admin/config/network      # 获取网络配置
PUT    /api/v1/admin/config/network      # 更新网络配置
POST   /api/v1/admin/config/backup       # 备份配置
POST   /api/v1/admin/config/restore      # 恢复配置
```

## 数据流设计

### 1. 用户服务器创建流程

```
用户请求创建服务器
    ↓
检查用户是否已有服务器 (每用户限一个)
    ↓
分配网络资源 (IP段、端口)
    ↓
创建网络命名空间
    ↓
生成 WireGuard 密钥对
    ↓
配置 WireGuard 接口
    ↓
设置防火墙规则
    ↓
保存配置到数据库
    ↓
返回服务器信息
```

### 2. 客户端管理流程

```
用户/管理员请求操作客户端
    ↓
验证权限 (用户只能操作自己的，管理员可操作所有)
    ↓
检查服务器状态 (必须启用)
    ↓
分配/释放 IP 地址
    ↓
生成/更新 WireGuard 配置
    ↓
应用配置到网络命名空间
    ↓
更新数据库记录
    ↓
清理/更新缓存
    ↓
记录审计日志
```

### 3. 管理员监控流程

```
定时任务启动
    ↓
遍历所有用户命名空间
    ↓
收集 WireGuard 统计信息
    ↓
解析连接状态和流量数据
    ↓
更新数据库统计记录
    ↓
更新 Redis 缓存
    ↓
检查告警条件
    ↓
发送告警通知 (如需要)
```

## 安全设计

### 1. 数据安全

#### 敏感信息加密
- **私钥加密**: WireGuard 私钥使用 AES-256 加密存储
- **预共享密钥**: PSK 同样加密存储
- **配置文件**: 临时生成，不持久化存储
- **传输加密**: API 通信使用 HTTPS

#### 访问控制
- **JWT 认证**: 基于现有认证系统
- **权限验证**: 每个操作都进行权限检查
- **资源隔离**: 用户只能访问自己的资源
- **操作审计**: 记录所有关键操作

### 2. 网络安全

#### 命名空间隔离
- **完全隔离**: 用户间网络完全隔离
- **防火墙规则**: iptables 规则防止跨用户访问
- **端口隔离**: 每个用户使用独立端口
- **流量监控**: 监控异常流量模式

#### 资源限制
- **连接数限制**: 限制每个用户的客户端数量
- **带宽限制**: 可配置的带宽限制
- **并发限制**: API 请求频率限制
- **资源配额**: CPU、内存使用限制

### 3. 系统安全

#### 输入验证
- **参数验证**: 所有输入参数严格验证
- **SQL 注入防护**: 使用参数化查询
- **XSS 防护**: 输出内容转义
- **CSRF 防护**: CSRF Token 验证

#### 系统加固
- **最小权限**: 服务以最小权限运行
- **日志记录**: 详细的操作和错误日志
- **监控告警**: 异常行为实时告警
- **定期备份**: 数据库和配置定期备份

## 性能优化设计

### 1. 缓存策略

#### Redis 缓存层次
```
L1 缓存: 热点配置数据 (TTL: 1小时)
├── 用户服务器配置
├── 活跃客户端列表
└── 实时统计数据

L2 缓存: 统计和监控数据 (TTL: 5分钟)
├── 流量统计
├── 连接状态
└── 系统性能指标

L3 缓存: 配置文件和QR码 (TTL: 24小时)
├── 客户端配置文件
├── QR码图片
└── 批量导出文件
```

#### 缓存更新策略
- **写入时更新**: 配置变更时立即更新缓存
- **定时刷新**: 统计数据定时批量更新
- **懒加载**: 首次访问时加载并缓存
- **缓存预热**: 系统启动时预加载热点数据

### 2. 数据库优化

#### 索引设计
```
user_wg_servers 集合:
- user_id (唯一索引)
- enabled + created_at (复合索引)

wg_clients 集合:
- user_id + server_id (复合索引)
- enabled + last_handshake (复合索引)
- ipv4_address (唯一索引)

audit_logs 集合:
- user_id + timestamp (复合索引)
- operation + timestamp (复合索引)
```

#### 查询优化
- **分页查询**: 大数据量列表使用游标分页
- **聚合查询**: 统计数据使用 MongoDB 聚合管道
- **读写分离**: 读多写少场景考虑读写分离
- **连接池**: 合理配置数据库连接池

### 3. 系统性能

#### 并发处理
- **协程池**: 限制并发协程数量
- **请求限流**: API 请求频率限制
- **批量操作**: 支持批量创建/更新/删除
- **异步处理**: 耗时操作异步执行

#### 资源管理
- **内存管理**: 及时释放不用的资源
- **文件句柄**: 控制文件句柄数量
- **网络连接**: 复用网络连接
- **临时文件**: 及时清理临时文件

## 监控告警设计

### 1. 系统监控指标

#### 基础指标
- **服务器数量**: 总数、启用数、禁用数
- **客户端数量**: 总数、在线数、离线数
- **流量统计**: 总流量、用户流量、实时带宽
- **连接统计**: 总连接数、活跃连接、异常连接

#### 性能指标
- **响应时间**: API 响应时间分布
- **吞吐量**: 每秒请求数、每秒事务数
- **错误率**: 4xx/5xx 错误比例
- **资源使用**: CPU、内存、磁盘、网络使用率

#### 业务指标
- **用户活跃度**: 日活、周活、月活用户
- **功能使用**: 各功能模块使用频率
- **异常行为**: 异常登录、大流量用户
- **系统健康**: 服务可用性、数据一致性

### 2. 告警规则设计

#### 系统告警
- **服务不可用**: 服务响应超时或返回错误
- **资源耗尽**: CPU/内存/磁盘使用率超过阈值
- **数据库异常**: 连接失败、查询超时
- **网络异常**: 网络不通、丢包率高

#### 业务告警
- **异常流量**: 用户流量超过正常范围
- **大量错误**: 错误率超过阈值
- **安全事件**: 异常登录、权限越界
- **配置异常**: WireGuard 配置错误

### 3. 日志设计

#### 日志分类
```
访问日志: 记录所有 API 请求
├── 请求时间、用户ID、接口路径
├── 请求参数、响应状态、响应时间
└── 客户端IP、User-Agent

操作日志: 记录关键业务操作
├── 服务器创建/删除/配置变更
├── 客户端添加/删除/状态变更
└── 管理员操作、批量操作

错误日志: 记录系统错误和异常
├── 系统异常、数据库错误
├── 网络错误、配置错误
└── 业务逻辑错误

审计日志: 记录安全相关操作
├── 用户登录/登出
├── 权限变更、敏感操作
└── 数据导出、配置备份
```

#### 日志格式
- **结构化日志**: 使用 JSON 格式便于解析
- **统一格式**: 所有日志使用统一的字段格式
- **链路追踪**: 支持请求链路追踪
- **日志轮转**: 按大小和时间轮转日志文件

## 后续优化建议

### 1. 性能优化
- **缓存策略**: 实施多级缓存提升响应速度
- **数据库优化**: 建立合适索引，优化查询性能
- **网络优化**: 优化 WireGuard 配置，减少延迟

### 2. 安全加固
- **权限细化**: 实施更细粒度的权限控制
- **审计日志**: 完善操作审计和安全日志
- **数据加密**: 敏感数据加密存储和传输

### 3. 运维监控
- **监控指标**: 建立完善的系统和业务监控
- **告警机制**: 设置关键指标告警
- **日志分析**: 结构化日志便于问题排查

### 4. 扩展性
- **水平扩展**: 支持多实例部署和负载均衡
- **模块化**: 功能模块化便于后续扩展
- **API 版本**: 支持 API 版本管理和向后兼容

## 总结

本设计方案提供了一个完整的 WireGuard 管理系统架构，主要特点包括：

### 核心特性
1. **用户隔离**: 每个用户拥有独立的网络命名空间和 WireGuard 服务器
2. **单服务器限制**: 每个用户只能创建一个服务器实例，简化管理复杂度
3. **管理员功能**: 管理员可以查看和管理所有用户的服务器和客户端
4. **完全集成**: 可以无缝集成到现有的 Gin + MongoDB + Redis 项目中

### 技术优势
1. **网络隔离**: 使用 Linux 网络命名空间实现用户间完全隔离
2. **权限控制**: 基于角色的权限管理，支持多级管理员
3. **数据安全**: 敏感数据加密存储，配置文件安全传输
4. **性能优化**: Redis 缓存提升响应速度，数据库索引优化查询

### 设计亮点
1. **架构清晰**: 分层架构设计，职责分离明确
2. **扩展性强**: 支持功能模块化扩展和水平扩展
3. **运维友好**: 完善的监控日志和错误处理机制
4. **安全可靠**: 多层安全防护和权限验证机制

该设计方案为 WireGuard 管理系统提供了坚实的架构基础，可以根据实际需求进行调整和优化。

// 禁用客户端
func disableClient(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
        return
    }

    if err := wgManager.DisableClient(c.Request.Context(), userID, clientID); err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, gin.H{"message": "Client disabled successfully"})
}

// 获取客户端统计
func getClientStats(c *gin.Context) {
    userID := getUserIDFromContext(c)
    wgManager := c.MustGet("wgManager").(WireGuardManager)

    clientID, err := primitive.ObjectIDFromHex(c.Param("id"))
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid client ID"})
        return
    }

    stats, err := wgManager.GetClientStats(c.Request.Context(), userID, clientID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, stats)
}

// 从上下文获取用户ID的辅助函数
func getUserIDFromContext(c *gin.Context) primitive.ObjectID {
    userIDStr, exists := c.Get("user_id")
    if !exists {
        panic("user_id not found in context")
    }

    userID, err := primitive.ObjectIDFromHex(userIDStr.(string))
    if err != nil {
        panic("invalid user_id format")
    }

    return userID
}
```

## 配置和部署

### 配置文件结构

```yaml
# config.yaml
mongodb:
  uri: "mongodb://localhost:27017"
  database: "wireguard_db"

redis:
  addr: "localhost:6379"
  password: ""
  db: 0

wireguard:
  base_port: 51820
  max_users: 1000
  max_servers_per_user: 5
  max_clients_per_server: 100
  base_network: "********/16"
  default_dns: ["*******", "*******"]
  default_mtu: 1420

security:
  encryption_key: "your-32-byte-encryption-key-here"

server:
  host: "0.0.0.0"
  port: 8080
  mode: "release" # debug, release
```

## 使用示例

### API 调用示例

```bash
# 1. 创建 WireGuard 服务器
curl -X POST http://localhost:8080/api/v1/wireguard/servers \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "My VPN Server",
    "endpoint": "vpn.example.com",
    "dns": ["*******", "*******"],
    "mtu": 1420
  }'

# 2. 创建客户端
curl -X POST http://localhost:8080/api/v1/wireguard/clients \
  -H "Authorization: Bearer your-jwt-token" \
  -H "Content-Type: application/json" \
  -d '{
    "server_id": "60f1b2c3d4e5f6789abcdef0",
    "name": "My Phone",
    "allowed_ips": ["0.0.0.0/0"],
    "persistent_keepalive": 25
  }'

# 3. 下载客户端配置
curl -X GET http://localhost:8080/api/v1/wireguard/configs/clients/60f1b2c3d4e5f6789abcdef1 \
  -H "Authorization: Bearer your-jwt-token" \
  -o client.conf

# 4. 获取 QR 码
curl -X GET http://localhost:8080/api/v1/wireguard/configs/clients/60f1b2c3d4e5f6789abcdef1/qrcode \
  -H "Authorization: Bearer your-jwt-token" \
  -o qrcode.png

# 5. 启用/禁用客户端
curl -X POST http://localhost:8080/api/v1/wireguard/clients/60f1b2c3d4e5f6789abcdef1/enable \
  -H "Authorization: Bearer your-jwt-token"

curl -X POST http://localhost:8080/api/v1/wireguard/clients/60f1b2c3d4e5f6789abcdef1/disable \
  -H "Authorization: Bearer your-jwt-token"
```

## 安全考虑

1. **权限隔离**: 用户只能访问自己的 WireGuard 资源
2. **密钥加密**: 私钥和预共享密钥使用 AES 加密存储
3. **网络隔离**: 通过命名空间和 iptables 规则确保用户间流量隔离
4. **资源限制**: 限制每个用户的服务器和客户端数量
5. **输入验证**: 所有 API 输入都经过严格验证
6. **审计日志**: 记录所有重要操作的日志

## 监控和维护

1. **统计收集**: 定期收集 WireGuard 统计信息
2. **健康检查**: 监控服务器和客户端状态
3. **自动清理**: 清理过期的客户端和配置
4. **备份策略**: 定期备份数据库和配置文件
5. **性能监控**: 监控系统资源使用情况

这个设计方案提供了完整的 WireGuard 管理功能，支持用户间网络隔离，可以很好地集成到你现有的 Gin + MongoDB + Redis 项目中。
